#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint voip24h_sdk_mobile.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'voip24h_sdk_mobile'
  s.version          = '0.0.1'
  s.summary          = 'Flutter Voip24h-SDK Mobile'
  s.description      = <<-DESC
Flutter Voip24h-SDK Mobile
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '9.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'

  s.dependency "linphone-sdk-novideo"
end
