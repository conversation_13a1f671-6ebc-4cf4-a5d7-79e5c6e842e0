// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBqTiY5hgiVxtw7LnOBpxiupCSW755fJCU',
    appId: '1:851166207276:web:93cd9404ca307b8c62a089',
    messagingSenderId: '851166207276',
    projectId: 'icontact-dialer',
    authDomain: 'icontact-dialer.firebaseapp.com',
    storageBucket: 'icontact-dialer.appspot.com',
    measurementId: 'G-CEVZ4EJ1B1',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD4Me9LiASWp-jwuhdKPFx8iomrZ89bmqE',
    appId: '1:851166207276:android:7f08add807e6801562a089',
    messagingSenderId: '851166207276',
    projectId: 'icontact-dialer',
    storageBucket: 'icontact-dialer.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDGsrfD4NA3ahXmGfcFmqzqEXk-2MZDxdc',
    appId: '1:851166207276:ios:18afc351693cf6c162a089',
    messagingSenderId: '851166207276',
    projectId: 'icontact-dialer',
    storageBucket: 'icontact-dialer.appspot.com',
    iosBundleId: 'com.example.dialercall',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDGsrfD4NA3ahXmGfcFmqzqEXk-2MZDxdc',
    appId: '1:851166207276:ios:18afc351693cf6c162a089',
    messagingSenderId: '851166207276',
    projectId: 'icontact-dialer',
    storageBucket: 'icontact-dialer.appspot.com',
    iosBundleId: 'com.example.dialercall',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBqTiY5hgiVxtw7LnOBpxiupCSW755fJCU',
    appId: '1:851166207276:web:c29d70a99fe3fa1662a089',
    messagingSenderId: '851166207276',
    projectId: 'icontact-dialer',
    authDomain: 'icontact-dialer.firebaseapp.com',
    storageBucket: 'icontact-dialer.appspot.com',
    measurementId: 'G-RFVQM4CLDM',
  );
}
