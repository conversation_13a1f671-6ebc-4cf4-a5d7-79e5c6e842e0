// import 'package:flutter/material.dart';
// import 'package:isalescrm/task/AddTask.dart';
//
//
// /*
//   Activity name : Dashboard
//   Project name : iSalesCRM Mobile App
//   Developer : Eng. M A Mazedul Islam
//   Designation : Senior Mobile App Developer at iHelpBD Dhaka, Bangladesh.
//   Email : <EMAIL>
//
//   Description : This Task provides the task list
// */
//
//
// class Task extends StatefulWidget {
//   const Task({Key? key}) : super(key: key);
//
//   @override
//   State<Task> createState() => _TaskState();
// }
//
// class _TaskState extends State<Task> {
//   @override
//   Widget build(BuildContext context) {
//
//     return Scaffold(
//
//       floatingActionButton : FloatingActionButton(
//         backgroundColor: Colors.deepPurpleAccent.shade400,
//         foregroundColor: Colors.white,
//         onPressed:(){
//           Navigator.push(context, MaterialPageRoute(builder: (context)=> const AddTask(isCallFromDashboard: false)));
//         },
//         child: const Icon(Icons.add,size: 30),
//       ),
//
//     );
//   }
// }
