name: record
description: Audio recorder from microphone to a given file path with multiple codecs, bit rate and sampling rate options.
version: 4.4.4
homepage: https://github.com/llfbandit/record/tree/master/record

environment:
  sdk: ">=2.15.1 <3.0.0"
  flutter: ">=2.8.1"

dependencies:
  flutter:
    sdk: flutter

  record_platform_interface: ^0.5.0
  record_web: '>=0.5.0 < 2.0.0'
  record_windows: '>=0.7.1 < 2.0.0'
  record_macos: '>=0.2.2 < 2.0.0'
  record_linux: '>=0.4.1 < 2.0.0'

# dependency_overrides:
#   record_platform_interface:
#     path: ../record_platform_interface
#   record_web:
#     path: ../record_web
#   record_windows:
#     path: ../record_windows
#   record_macos:
#     path: ../record_macos
#   record_linux:
#     path: ../record_linux

dev_dependencies:
  # https://pub.dev/packages/flutter_lints
  flutter_lints: ^2.0.0

# The following section is specific to Flutter.
flutter:
  plugin:
    platforms:
      android:
        package: com.llfbandit.record
        pluginClass: RecordPlugin
      ios:
        pluginClass: RecordPlugin
      web:
        default_package: record_web
      windows:
        default_package: record_windows
      macos:
        default_package: record_macos
      linux:
        default_package: record_linux
