name: dialercall
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  animated_splash_screen: ^1.3.0
  shared_preferences: ^2.3.2
  simple_fontellico_progress_dialog: ^0.3.0
#  curved_navigation_bar:
  flutter_animated_button: ^2.0.3
  animated_gradient:
  flutter_screenutil: ^5.9.3
  get:
  firebase_core: 
  firebase_auth:
  firebase_database:
  cloud_firestore: 
  firebase_messaging: 
  flutter_local_notifications: ^17.2.2
  internet_connection_checker_plus: ^1.0.1
  flutter_background_service: ^2.4.6
  flutter_background_service_android: ^3.0.3




  #VOIP SDK
  voip24h_sdk_mobile: 
    path: ./voip24h_sdk_mobile

  lite_rolling_switch: ^1.0.1

  #For get permission_handler_windows:
  permission_handler: ^11.3.1

  #For Direct call
  flutter_phone_direct_caller: ^2.1.1

  #Flutter SQLite database
  sqflite: ^2.3.3+1

  path_provider: ^2.1.4

  #Call recorder
  record:
    path: ./record

  #Animated text
  marquee: ^2.2.3

  #dropdown_search_
  dropdown_search: ^5.0.6

  #for url open
  url_launcher: ^6.3.0

  #Get phone contact
  flutter_contacts: ^1.1.9+2

  #Animated Icon
  animated_icon: 0.0.6
  flutter_callkit_incoming: ^2.0.4+1
  uuid: ^4.5.0
  animated_text_kit: ^4.2.2


dependency_overrides:
  fading_edge_scrollview: ^4.1.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

  
  flutter_launcher_icons: ^0.13.1


flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"
 
  

# flutter_launcher_icons:
#     android: "launcher_icon"
#     ios: true
#     image_path: "assets/images/logo.png"
    
    
#    min_sdk_android: 21 # android min sdk 


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/



  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
